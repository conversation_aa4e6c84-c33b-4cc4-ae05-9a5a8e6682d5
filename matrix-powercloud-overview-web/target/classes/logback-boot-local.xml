<configuration>
    <!--定义通用日志输出格式
        格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度,%msg：日志消息，%n是换行符
    -->
    <property name="LOG.PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %yellow(-[%5p]) ---- traceId:[%X{X-B3-TraceId:-},%X{X-B3-SpanId:-}] - %blue(${PID}) %yellow([%thread]) %green(%-40.40logger{40}) -  %msg%n "/>

    <!--配置控制台输出-->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG.PATTERN}</pattern>
            <!-- 控制台也要使用UTF-8，不要使用GBK，否则会中文乱码 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--配置root日志打印-->
    <root level="info">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>